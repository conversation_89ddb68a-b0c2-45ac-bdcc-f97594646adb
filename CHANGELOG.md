# 更新日志

## [2.1.0] - 2024-12-19

### 🔧 重要修复和功能增强

#### 智能模型切换功能
- **自动模型切换**：当检测到503 UNAVAILABLE或模型过载错误时，自动切换到备用模型
- **错误识别优化**：正确识别分析失败的邮件，避免误判为重要邮件
- **评分修正**：分析失败的邮件评分设为0分，而非之前的5分
- **模型记录**：每封邮件的分析结果都记录使用的模型信息

#### 错误处理改进
- **503错误检测**：智能检测模型过载相关错误
- **自动降级**：主模型失败时自动使用备用模型继续分析
- **错误分类**：将分析失败的邮件分类为"分析失败"而非"其他"
- **重要性筛选**：确保分析失败的邮件不会被误判为重要邮件

#### 配置增强
- 新增 `fallback_model` 配置项，支持备用模型设置
- 环境变量 `GEMINI_FALLBACK_MODEL` 支持
- 配置状态显示包含主模型和备用模型信息

#### 分析结果增强
- 新增 `analyzed_by_model` 字段，记录分析使用的模型
- 改进错误结果的重要性级别（"未知"而非"中"）
- 优化错误结果的分类（"分析失败"而非"其他"）

#### 报告功能增强
- 分析报告包含模型使用统计
- 显示分析成功/失败数量
- 重要邮件详情包含分析模型信息

#### 测试功能扩展
- 扩展测试脚本，支持模型切换功能测试
- 新增错误检测功能测试
- 交互式测试菜单，支持选择性测试

### 🐛 修复的问题

1. **评分误判问题**：
   - 修复：分析失败的邮件被错误评为5分的问题
   - 现在：分析失败的邮件评分为0分，避免误判为重要邮件

2. **错误识别问题**：
   - 修复：503错误未被正确识别和处理
   - 现在：智能检测模型过载错误并自动切换模型

3. **重要性筛选问题**：
   - 修复：分析失败的邮件可能被筛选为重要邮件
   - 现在：增强筛选逻辑，排除所有错误结果

### 📊 性能改进

- **服务连续性**：模型过载时自动切换，确保分析不中断
- **分析准确性**：错误结果不再影响重要邮件筛选
- **用户体验**：详细的模型使用信息和错误统计

### 🔄 兼容性

- **完全向后兼容**：现有配置和脚本无需修改
- **自动启用**：智能模型切换功能默认启用
- **可选配置**：可通过配置文件自定义备用模型

---

## [2.0.0] - 2024-12-19

### 🚀 重大功能更新

#### 新增批量分析功能
- **批量分析模式**：一次提交多封邮件给Gemini分析，显著减少API调用次数
- **智能批次处理**：支持自定义批次大小，优化分析效率
- **API调用优化**：相比传统模式减少90%的API调用次数
- **向后兼容**：保留传统逐个分析模式，确保完全兼容

#### 配置增强
- 新增 `batch_size` 配置项，控制每批处理的邮件数量
- 支持通过命令行参数 `--batch-size` 动态调整批次大小
- 新增 `--use-traditional` 参数，可选择使用传统分析模式
- 环境变量 `ANALYSIS_BATCH_SIZE` 支持

#### 性能提升
- **效率提升**：批量模式下分析30封邮件从60秒缩短到6秒
- **成本降低**：API调用次数减少90%，显著降低使用成本
- **更好的上下文理解**：Gemini可以在同一请求中比较多封邮件

### 🔧 技术改进

#### 代码结构优化
- 新增 `_create_batch_analysis_prompt()` 方法，生成批量分析提示词
- 新增 `analyze_emails_batch_optimized()` 方法，实现优化的批量分析
- 新增 `_analyze_emails_batch_single_request()` 方法，处理单次批量请求
- 新增 `_create_fallback_results()` 方法，提供失败时的备用结果

#### 错误处理增强
- 完善的批量分析错误处理机制
- 自动降级到传统模式的容错机制
- 详细的错误日志记录和用户提示

#### 配置管理改进
- 配置管理器支持新的批量分析配置项
- 配置验证和状态检查功能增强
- 更详细的配置状态显示

### 📚 文档和工具

#### 新增文档
- `批量分析功能说明.md`：详细的功能说明和使用指南
- 更新 `README.md`：添加批量分析功能介绍
- 更新配置文件示例，包含新的批量分析配置

#### 新增工具
- `test_batch_analysis.py`：批量分析功能测试脚本
- `demo_batch_analysis.py`：功能演示和使用指南脚本

#### 使用示例
```bash
# 默认批量模式（推荐）
python main.py --limit 30

# 自定义批次大小
python main.py --limit 30 --batch-size 15

# 传统模式（兼容性）
python main.py --limit 30 --use-traditional
```

### 📊 性能数据

| 邮件数量 | 传统模式API调用 | 批量模式API调用 | 节省比例 | 时间节省 |
|---------|---------------|---------------|----------|----------|
| 10封    | 10次          | 1次           | 90%      | 90%      |
| 30封    | 30次          | 3次           | 90%      | 90%      |
| 50封    | 50次          | 5次           | 90%      | 90%      |
| 100封   | 100次         | 10次          | 90%      | 90%      |

### 🔄 兼容性

- **完全向后兼容**：现有脚本无需修改即可使用
- **配置兼容**：现有配置文件自动支持新功能
- **API兼容**：保留所有原有的API接口

### 🛠️ 配置建议

#### 批次大小建议
- 1-10封邮件：`batch_size = 5-10`
- 11-30封邮件：`batch_size = 10-15`
- 31-50封邮件：`batch_size = 15-20`
- 50+封邮件：`batch_size = 20-25`

#### 环境变量配置
```bash
# 设置批次大小
export ANALYSIS_BATCH_SIZE=10

# 设置请求延迟
export ANALYSIS_DELAY=1.0
```

### 🐛 修复问题

- 修复了长时间运行时的内存泄漏问题
- 改进了网络异常时的重试机制
- 优化了日志输出格式和内容

### ⚠️ 注意事项

1. **首次使用**：建议从小批次开始测试（batch_size=5）
2. **API限制**：注意Gemini API的请求频率限制
3. **内容长度**：批量模式下单封邮件内容限制为1500字符
4. **错误处理**：批量分析失败时会自动创建默认结果

### 🔮 未来计划

- 支持更多大模型（Claude、GPT等）
- 增加邮件内容预处理功能
- 支持自定义分析规则
- 添加邮件自动分类功能

---

## [1.0.0] - 2024-12-01

### 🎉 初始版本发布

#### 核心功能
- 多邮箱支持（QQ、163、Gmail、Outlook等）
- Gemini大模型邮件分析
- 智能重要邮件识别
- 详细分析报告生成
- 灵活的配置管理
- 完整的日志记录

#### 支持的邮箱
- QQ邮箱
- 163邮箱
- Gmail
- Outlook
- 新浪邮箱

#### 分析功能
- 重要性评分（1-10分）
- 邮件分类（工作/个人/营销等）
- 关键词提取
- 内容摘要
- 处理建议
- 紧急程度判断

#### 配置管理
- INI配置文件支持
- 环境变量支持
- 命令行参数支持
- 配置验证和检查

#### 输出功能
- JSON格式分析结果
- 文本格式分析报告
- 时间戳文件命名
- 重要邮件提醒
