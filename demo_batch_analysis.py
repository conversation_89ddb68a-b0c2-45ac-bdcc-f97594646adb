#!/usr/bin/env python3
"""
批量分析功能演示脚本
展示新的批量分析功能的使用方法和效果
"""

import os
import sys
import time
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def print_header(title):
    """打印标题"""
    print("\n" + "="*60)
    print(f"🎯 {title}")
    print("="*60)


def print_step(step, description):
    """打印步骤"""
    print(f"\n📋 步骤 {step}: {description}")
    print("-" * 40)


def demo_traditional_vs_batch():
    """演示传统模式与批量模式的对比"""
    print_header("批量分析功能演示")
    
    print("🚀 欢迎使用邮件分析系统的新批量分析功能！")
    print("本演示将展示传统模式与批量模式的区别和优势。")
    
    # 检查环境
    print_step(1, "环境检查")
    
    if not os.getenv('GEMINI_API_KEY'):
        print("❌ 未设置GEMINI_API_KEY环境变量")
        print("请先设置API密钥：")
        print("   Windows: set GEMINI_API_KEY=your_api_key")
        print("   Linux/Mac: export GEMINI_API_KEY=your_api_key")
        return
    else:
        print("✅ GEMINI_API_KEY 已设置")
    
    # 检查配置文件
    if os.path.exists('email_config.ini'):
        print("✅ 配置文件存在")
    else:
        print("⚠️ 配置文件不存在，将使用默认配置")
    
    # 演示命令
    print_step(2, "使用方法演示")
    
    print("🔍 传统模式（逐个分析）：")
    print("   python main.py --limit 10 --use-traditional")
    print("   特点：每封邮件单独调用一次API")
    print("   API调用次数：10封邮件 = 10次调用")
    
    print("\n🚀 批量模式（推荐）：")
    print("   python main.py --limit 10 --batch-size 5")
    print("   特点：每批邮件调用一次API")
    print("   API调用次数：10封邮件 = 2次调用（节省80%）")
    
    print("\n⚡ 超级批量模式：")
    print("   python main.py --limit 10 --batch-size 10")
    print("   特点：所有邮件一次性分析")
    print("   API调用次数：10封邮件 = 1次调用（节省90%）")
    
    # 性能对比
    print_step(3, "性能对比分析")
    
    scenarios = [
        {"emails": 10, "batch_size": 5, "traditional": 10, "batch": 2},
        {"emails": 30, "batch_size": 10, "traditional": 30, "batch": 3},
        {"emails": 50, "batch_size": 15, "traditional": 50, "batch": 4},
        {"emails": 100, "batch_size": 20, "traditional": 100, "batch": 5},
    ]
    
    print("📊 不同邮件数量下的API调用对比：")
    print(f"{'邮件数量':<8} {'传统模式':<8} {'批量模式':<8} {'节省比例':<8} {'时间节省'}")
    print("-" * 50)
    
    for scenario in scenarios:
        emails = scenario["emails"]
        traditional = scenario["traditional"]
        batch = scenario["batch"]
        savings = (traditional - batch) / traditional * 100
        time_savings = f"{savings:.0f}%"
        
        print(f"{emails:<8} {traditional:<8} {batch:<8} {savings:.0f}%{'':<4} {time_savings}")
    
    # 实际使用建议
    print_step(4, "使用建议")
    
    print("💡 批次大小选择建议：")
    print("   • 1-10封邮件：batch-size = 5-10")
    print("   • 11-30封邮件：batch-size = 10-15")
    print("   • 31-50封邮件：batch-size = 15-20")
    print("   • 50+封邮件：batch-size = 20-25")
    
    print("\n⚠️ 注意事项：")
    print("   • 批次太小：无法充分利用批量优势")
    print("   • 批次太大：可能超出API限制")
    print("   • 首次使用：建议从小批次开始测试")
    
    # 实际演示
    print_step(5, "实际演示")
    
    print("🎬 现在让我们运行一个实际的演示...")
    print("请选择要演示的模式：")
    print("1. 传统模式演示（3封邮件）")
    print("2. 批量模式演示（10封邮件，批次大小=5）")
    print("3. 超级批量模式演示（10封邮件，批次大小=10）")
    print("4. 跳过演示")
    
    try:
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            print("\n🔍 运行传统模式演示...")
            os.system("python test_batch_analysis.py")
        elif choice == "2":
            print("\n🚀 运行批量模式演示...")
            print("命令：python main.py --limit 10 --batch-size 5")
            print("（请确保已配置邮箱信息）")
        elif choice == "3":
            print("\n⚡ 运行超级批量模式演示...")
            print("命令：python main.py --limit 10 --batch-size 10")
            print("（请确保已配置邮箱信息）")
        elif choice == "4":
            print("\n⏭️ 跳过演示")
        else:
            print("\n❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n⚠️ 演示被中断")
    
    # 总结
    print_step(6, "功能总结")
    
    print("🎉 批量分析功能的主要优势：")
    print("   ✅ 减少90%的API调用次数")
    print("   ✅ 显著提高分析效率")
    print("   ✅ 降低API使用成本")
    print("   ✅ 更好的上下文理解")
    print("   ✅ 完全向后兼容")
    
    print("\n📚 更多信息：")
    print("   • 详细说明：批量分析功能说明.md")
    print("   • 测试脚本：test_batch_analysis.py")
    print("   • 配置示例：email_config.ini")
    
    print("\n🚀 开始使用：")
    print("   python main.py --limit 20 --batch-size 10")
    
    print_header("演示结束")
    print("感谢使用邮件分析系统！")


def show_quick_start():
    """显示快速开始指南"""
    print_header("快速开始指南")
    
    print("🚀 5分钟快速上手批量分析功能：")
    
    steps = [
        "设置API密钥：export GEMINI_API_KEY=your_key",
        "配置邮箱信息：编辑 email_config.ini",
        "运行批量分析：python main.py --limit 20 --batch-size 10",
        "查看分析结果：analysis_results_*.json",
        "阅读分析报告：analysis_report_*.txt"
    ]
    
    for i, step in enumerate(steps, 1):
        print(f"\n{i}. {step}")
    
    print("\n✨ 就是这么简单！")


def main():
    """主函数"""
    print("🎯 邮件分析系统 - 批量分析功能演示")
    print("选择演示内容：")
    print("1. 完整功能演示")
    print("2. 快速开始指南")
    print("3. 退出")
    
    try:
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == "1":
            demo_traditional_vs_batch()
        elif choice == "2":
            show_quick_start()
        elif choice == "3":
            print("\n👋 再见！")
        else:
            print("\n❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n\n👋 再见！")
    except Exception as e:
        print(f"\n❌ 演示过程中出错: {e}")


if __name__ == "__main__":
    main()
