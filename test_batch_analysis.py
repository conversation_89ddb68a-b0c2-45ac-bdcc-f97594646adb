#!/usr/bin/env python3
"""
邮件分析功能测试脚本
用于验证批量分析功能和模型切换功能是否正常工作
"""

import os
import sys
import json
import time
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from email_analyzer import EmailAnalyzer


def create_test_emails(count=15):
    """创建测试邮件数据"""
    test_emails = []
    
    email_templates = [
        {
            'subject': '紧急：项目截止日期提醒',
            'from': '<EMAIL>',
            'content': '请注意，项目A的截止日期是明天，请确保按时完成所有任务。',
            'expected_importance': 'high'
        },
        {
            'subject': '会议通知：周例会',
            'from': '<EMAIL>',
            'content': '本周例会将于周三下午2点在会议室A举行，请准时参加。',
            'expected_importance': 'medium'
        },
        {
            'subject': '营销推广：限时优惠',
            'from': '<EMAIL>',
            'content': '限时优惠活动，全场商品8折，仅限今日！立即购买享受优惠。',
            'expected_importance': 'low'
        },
        {
            'subject': '银行通知：账户余额提醒',
            'from': '<EMAIL>',
            'content': '您的账户余额已低于1000元，请及时充值以避免影响正常使用。',
            'expected_importance': 'high'
        },
        {
            'subject': '系统维护通知',
            'from': '<EMAIL>',
            'content': '系统将于本周末进行维护升级，预计停机2小时，请提前做好准备。',
            'expected_importance': 'medium'
        }
    ]
    
    for i in range(count):
        template = email_templates[i % len(email_templates)]
        email = {
            'id': f'test_email_{i+1}',
            'subject': f"{template['subject']} #{i+1}",
            'from': template['from'],
            'date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'content': template['content'],
            'expected_importance': template['expected_importance']
        }
        test_emails.append(email)
    
    return test_emails


def test_traditional_mode(analyzer, emails):
    """测试传统分析模式"""
    print("\n" + "="*50)
    print("🔍 测试传统分析模式")
    print("="*50)
    
    start_time = time.time()
    
    # 只测试前3封邮件以节省时间
    test_emails = emails[:3]
    results = analyzer.analyze_emails_batch(
        test_emails, 
        delay=0.5, 
        use_optimized=False
    )
    
    end_time = time.time()
    
    print(f"✅ 传统模式完成")
    print(f"📧 分析邮件数量: {len(test_emails)}")
    print(f"✨ 成功分析数量: {len(results)}")
    print(f"⏱️ 耗时: {end_time - start_time:.2f} 秒")
    print(f"🔄 预计API调用次数: {len(test_emails)}")
    
    return results


def test_batch_mode(analyzer, emails, batch_size=5):
    """测试批量分析模式"""
    print("\n" + "="*50)
    print(f"🚀 测试批量分析模式 (批次大小: {batch_size})")
    print("="*50)
    
    start_time = time.time()
    
    results = analyzer.analyze_emails_batch(
        emails, 
        delay=0.5, 
        batch_size=batch_size,
        use_optimized=True
    )
    
    end_time = time.time()
    
    expected_api_calls = (len(emails) + batch_size - 1) // batch_size
    
    print(f"✅ 批量模式完成")
    print(f"📧 分析邮件数量: {len(emails)}")
    print(f"✨ 成功分析数量: {len(results)}")
    print(f"⏱️ 耗时: {end_time - start_time:.2f} 秒")
    print(f"🔄 预计API调用次数: {expected_api_calls}")
    print(f"📊 效率提升: {((len(emails) - expected_api_calls) / len(emails) * 100):.1f}%")
    
    return results


def analyze_results(results, test_emails):
    """分析测试结果"""
    print("\n" + "="*50)
    print("📊 结果分析")
    print("="*50)
    
    if not results:
        print("❌ 没有分析结果")
        return
    
    # 统计分析成功率
    successful_results = [r for r in results if not r.get('error', False)]
    success_rate = len(successful_results) / len(results) * 100
    
    print(f"✅ 分析成功率: {success_rate:.1f}% ({len(successful_results)}/{len(results)})")
    
    # 重要性分布
    importance_levels = {}
    for result in successful_results:
        level = result.get('importance_level', '未知')
        importance_levels[level] = importance_levels.get(level, 0) + 1
    
    print(f"\n📈 重要性分布:")
    for level, count in importance_levels.items():
        print(f"  {level}: {count} 封")
    
    # 显示前几个结果
    print(f"\n📋 分析结果示例:")
    for i, result in enumerate(successful_results[:3], 1):
        print(f"\n  {i}. {result.get('email_subject', '无主题')}")
        print(f"     重要性: {result.get('importance_level', '未知')} (评分: {result.get('importance_score', 0)})")
        print(f"     分类: {result.get('category', '未知')}")
        print(f"     原因: {result.get('reason', '无原因')[:100]}...")


def test_model_switching(analyzer):
    """测试模型切换功能"""
    print("\n" + "="*50)
    print("🔄 测试模型切换功能")
    print("="*50)

    print(f"初始模型: {analyzer.current_model}")
    print(f"备用模型: {analyzer.fallback_model}")
    print(f"是否已切换: {analyzer.model_switched}")

    # 模拟503错误
    test_error_messages = [
        "503 UNAVAILABLE. {'error': {'code': 503, 'message': 'The model is overloaded. Please try again later.', 'status': 'UNAVAILABLE'}}",
        "The model is overloaded",
        "UNAVAILABLE",
        "正常错误消息"
    ]

    print("\n🧪 测试错误检测:")
    for i, error_msg in enumerate(test_error_messages, 1):
        should_switch = analyzer._should_switch_model(error_msg)
        print(f"  {i}. 错误: {error_msg[:50]}...")
        print(f"     应该切换模型: {'是' if should_switch else '否'}")

    # 测试模型切换
    print("\n🔄 测试模型切换:")
    original_model = analyzer.current_model
    analyzer._switch_to_fallback_model()
    print(f"切换后模型: {analyzer.current_model}")
    print(f"是否已切换: {analyzer.model_switched}")

    # 再次切换（应该不变）
    analyzer._switch_to_fallback_model()
    print(f"再次切换后模型: {analyzer.current_model}")

    return True


def test_error_analysis_detection(analyzer):
    """测试错误分析结果检测"""
    print("\n" + "="*50)
    print("🔍 测试错误分析结果检测")
    print("="*50)

    # 创建测试结果
    test_results = [
        {
            'importance_score': 5,
            'importance_level': '中',
            'summary': '正常分析结果',
            'reason': '这是一封工作邮件'
        },
        {
            'importance_score': 5,
            'importance_level': '中',
            'summary': '分析失败',
            'reason': '分析过程出错: 503 UNAVAILABLE',
            'error': True
        },
        {
            'importance_score': 0,
            'importance_level': '未知',
            'summary': '批量分析失败',
            'reason': 'API响应解析错误'
        },
        {
            'importance_score': 8,
            'importance_level': '高',
            'summary': '重要邮件',
            'reason': '包含紧急任务'
        }
    ]

    print("🧪 测试错误检测:")
    for i, result in enumerate(test_results, 1):
        is_error = analyzer._is_analysis_error(result)
        print(f"  {i}. 结果: {result.get('summary', '无摘要')}")
        print(f"     是否为错误: {'是' if is_error else '否'}")
        print(f"     评分: {result.get('importance_score', 0)}")

    return True


def show_test_menu():
    """显示测试菜单"""
    print("\n" + "="*60)
    print("🧪 邮件分析功能测试菜单")
    print("="*60)
    print("请选择要测试的功能:")
    print("1. 传统分析模式测试")
    print("2. 批量分析模式测试 (批次大小: 5)")
    print("3. 批量分析模式测试 (批次大小: 10)")
    print("4. 模型切换功能测试")
    print("5. 错误检测功能测试")
    print("6. 完整功能测试")
    print("7. 退出")

    try:
        choice = input("\n请输入选择 (1-7): ").strip()
        return choice
    except KeyboardInterrupt:
        return "7"


def main():
    """主测试函数"""
    print("🧪 邮件分析功能测试系统")
    print("="*60)
    
    # 检查API密钥
    if not os.getenv('GEMINI_API_KEY'):
        print("❌ 请设置GEMINI_API_KEY环境变量")
        print("   export GEMINI_API_KEY=your_api_key")
        return

    # 初始化分析器
    print("🔧 初始化邮件分析器...")
    analyzer = EmailAnalyzer()

    if not analyzer.client:
        print("❌ 分析器初始化失败")
        return

    # 创建测试邮件
    print("📧 创建测试邮件...")
    test_emails = create_test_emails(15)
    print(f"✅ 创建了 {len(test_emails)} 封测试邮件")

    try:
        while True:
            choice = show_test_menu()

            if choice == "1":
                # 测试传统模式
                traditional_results = test_traditional_mode(analyzer, test_emails)
                analyze_results(traditional_results, test_emails[:3])

            elif choice == "2":
                # 测试批量模式 (批次大小: 5)
                batch_results = test_batch_mode(analyzer, test_emails, batch_size=5)
                analyze_results(batch_results, test_emails)

            elif choice == "3":
                # 测试批量模式 (批次大小: 10)
                batch_results = test_batch_mode(analyzer, test_emails, batch_size=10)
                analyze_results(batch_results, test_emails)

            elif choice == "4":
                # 测试模型切换功能
                test_model_switching(analyzer)

            elif choice == "5":
                # 测试错误检测功能
                test_error_analysis_detection(analyzer)

            elif choice == "6":
                # 完整功能测试
                print("\n🚀 开始完整功能测试...")

                # 测试传统模式（少量邮件）
                traditional_results = test_traditional_mode(analyzer, test_emails)

                # 测试批量模式
                batch_results_5 = test_batch_mode(analyzer, test_emails, batch_size=5)
                batch_results_10 = test_batch_mode(analyzer, test_emails, batch_size=10)

                # 测试模型切换
                test_model_switching(analyzer)

                # 测试错误检测
                test_error_analysis_detection(analyzer)

                # 分析结果
                print("\n" + "="*60)
                print("📊 传统模式结果分析")
                analyze_results(traditional_results, test_emails[:3])

                print("\n" + "="*60)
                print("📊 批量模式结果分析 (批次大小: 5)")
                analyze_results(batch_results_5, test_emails)

                print("\n" + "="*60)
                print("📊 批量模式结果分析 (批次大小: 10)")
                analyze_results(batch_results_10, test_emails)

                # 保存测试结果
                test_results = {
                    'timestamp': datetime.now().isoformat(),
                    'test_emails_count': len(test_emails),
                    'analyzer_info': {
                        'initial_model': analyzer.model_name,
                        'current_model': analyzer.current_model,
                        'fallback_model': analyzer.fallback_model,
                        'model_switched': analyzer.model_switched
                    },
                    'traditional_mode': {
                        'emails_tested': len(test_emails[:3]),
                        'results_count': len(traditional_results),
                        'api_calls': len(test_emails[:3])
                    },
                    'batch_mode_5': {
                        'emails_tested': len(test_emails),
                        'results_count': len(batch_results_5),
                        'api_calls': (len(test_emails) + 4) // 5
                    },
                    'batch_mode_10': {
                        'emails_tested': len(test_emails),
                        'results_count': len(batch_results_10),
                        'api_calls': (len(test_emails) + 9) // 10
                    }
                }

                with open('test_results.json', 'w', encoding='utf-8') as f:
                    json.dump(test_results, f, ensure_ascii=False, indent=2)

                print(f"\n💾 测试结果已保存到: test_results.json")
                print("\n✅ 完整测试完成！")

            elif choice == "7":
                print("\n👋 退出测试")
                break

            else:
                print("\n❌ 无效选择，请重新输入")

    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")


if __name__ == "__main__":
    main()
