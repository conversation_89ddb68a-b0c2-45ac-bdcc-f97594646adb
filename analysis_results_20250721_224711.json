{"timestamp": "2025-07-21T22:47:11.294539", "total_emails": 10, "analyzed_emails": 10, "important_emails": 0, "config": {"folder": "INBOX", "limit": 10, "min_score": 7}, "results": [{"email_id": "81", "email_subject": "更安全、更高效、更强大，尽在QQ邮箱APP", "email_from": "QQ邮箱团队 <<EMAIL>>", "email_date": "Mon, 21 Jul 2025 22:30:15 +0800", "importance_level": "中", "importance_score": 5, "category": "其他", "summary": "批量分析失败", "reason": "分析过程出错: 503 UNAVAILABLE. {'error': {'code': 503, 'message': 'The model is overloaded. Please try again later.', 'status': 'UNAVAILABLE'}}", "analysis_time": "2025-07-21T22:47:11.292107", "batch_index": 0, "error": true}, {"email_id": "80", "email_subject": "您的 Dashlane 帐户将在 10天内删除", "email_from": "<PERSON><PERSON> <<EMAIL>>", "email_date": "Mon, 21 Jul 2025 12:00:08 +0000 (UTC)", "importance_level": "中", "importance_score": 5, "category": "其他", "summary": "批量分析失败", "reason": "分析过程出错: 503 UNAVAILABLE. {'error': {'code': 503, 'message': 'The model is overloaded. Please try again later.', 'status': 'UNAVAILABLE'}}", "analysis_time": "2025-07-21T22:47:11.292115", "batch_index": 1, "error": true}, {"email_id": "79", "email_subject": "=?utf-8*ja?Q?=E3=80=90Ci-en=E3=80=91_?=\r\n =?utf-8*ja?Q?=E6=96=B0=E7=9D=80=E8=A8=98=E4=BA=8B?=\r\n =?utf-8*ja?Q?=E3=81=AE=E3=81=8A=E7=9F=A5=E3=82=89=E3=81=9B?= [2025/07/21]", "email_from": "\"Ci-en\" <<EMAIL>>", "email_date": "Mon, 21 Jul 2025 19:53:40 +0900", "importance_level": "中", "importance_score": 5, "category": "其他", "summary": "批量分析失败", "reason": "分析过程出错: 503 UNAVAILABLE. {'error': {'code': 503, 'message': 'The model is overloaded. Please try again later.', 'status': 'UNAVAILABLE'}}", "analysis_time": "2025-07-21T22:47:11.292119", "batch_index": 2, "error": true}, {"email_id": "78", "email_subject": "Your Google Account is no longer recoverable", "email_from": "Google <<EMAIL>>", "email_date": "Sun, 20 Jul 2025 19:41:18 GMT", "importance_level": "中", "importance_score": 5, "category": "其他", "summary": "批量分析失败", "reason": "分析过程出错: 503 UNAVAILABLE. {'error': {'code': 503, 'message': 'The model is overloaded. Please try again later.', 'status': 'UNAVAILABLE'}}", "analysis_time": "2025-07-21T22:47:11.292121", "batch_index": 3, "error": true}, {"email_id": "77", "email_subject": "欢迎回家，注册交易送最高100元BTC", "email_from": "\"HTX\" <<EMAIL>>", "email_date": "Sat, 19 Jul 2025 10:39:27 +0000 (UTC)", "importance_level": "中", "importance_score": 5, "category": "其他", "summary": "批量分析失败", "reason": "分析过程出错: 503 UNAVAILABLE. {'error': {'code': 503, 'message': 'The model is overloaded. Please try again later.', 'status': 'UNAVAILABLE'}}", "analysis_time": "2025-07-21T22:47:11.292123", "batch_index": 4, "error": true}, {"email_id": "76", "email_subject": "『公募基金上半年业绩分析与策略展望』--2025年二季度富国快讯", "email_from": "富国基金 <<EMAIL>>", "email_date": "", "importance_level": "中", "importance_score": 5, "category": "其他", "summary": "批量分析失败", "reason": "分析过程出错: 503 UNAVAILABLE. {'error': {'code': 503, 'message': 'The model is overloaded. Please try again later.', 'status': 'UNAVAILABLE'}}", "analysis_time": "2025-07-21T22:47:11.292125", "batch_index": 5, "error": true}, {"email_id": "75", "email_subject": "Cloudflare Access login code for atreide.cloudflareaccess.com", "email_from": "\"Cloudflare\" <<EMAIL>>", "email_date": "<PERSON><PERSON>, 18 Jul 2025 17:29:25 +0000", "importance_level": "中", "importance_score": 5, "category": "其他", "summary": "批量分析失败", "reason": "分析过程出错: 503 UNAVAILABLE. {'error': {'code': 503, 'message': 'The model is overloaded. Please try again later.', 'status': 'UNAVAILABLE'}}", "analysis_time": "2025-07-21T22:47:11.292127", "batch_index": 6, "error": true}, {"email_id": "74", "email_subject": "招商基金管理有限公司旗下基金2025年第2季度报告提示性公告", "email_from": "招商基金<<EMAIL>>", "email_date": "Fri, 18 Jul 2025 17:09:24 +0800", "importance_level": "中", "importance_score": 5, "category": "其他", "summary": "批量分析失败", "reason": "分析过程出错: 503 UNAVAILABLE. {'error': {'code': 503, 'message': 'The model is overloaded. Please try again later.', 'status': 'UNAVAILABLE'}}", "analysis_time": "2025-07-21T22:47:11.292128", "batch_index": 7, "error": true}, {"email_id": "73", "email_subject": "『才能に打ち砕かれた日から、僕の最強は始…』など今週注目の小説", "email_from": "\"カクヨム\" <<EMAIL>>", "email_date": "Fri, 18 Jul 2025 11:07:00 +0000 (UTC)", "importance_level": "中", "importance_score": 5, "category": "其他", "summary": "批量分析失败", "reason": "分析过程出错: 503 UNAVAILABLE. {'error': {'code': 503, 'message': 'The model is overloaded. Please try again later.', 'status': 'UNAVAILABLE'}}", "analysis_time": "2025-07-21T22:47:11.292130", "batch_index": 8, "error": true}, {"email_id": "72", "email_subject": "基金交易确认", "email_from": "中欧基金 <<EMAIL>>", "email_date": "Fri, 18 Jul 2025 09:27:32 +0800", "importance_level": "中", "importance_score": 5, "category": "其他", "summary": "批量分析失败", "reason": "分析过程出错: 503 UNAVAILABLE. {'error': {'code': 503, 'message': 'The model is overloaded. Please try again later.', 'status': 'UNAVAILABLE'}}", "analysis_time": "2025-07-21T22:47:11.292131", "batch_index": 9, "error": true}]}