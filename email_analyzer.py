"""
邮件分析模块
使用Gemini大模型分析邮件内容，识别重要邮件
"""

from google import genai
import logging
from typing import Dict, List, Optional
import json
import time
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class EmailAnalyzer:
    """邮件分析器类"""
    
    def __init__(self, model_name: str = "gemini-2.5-pro"):
        """
        初始化邮件分析器

        Args:
            model_name: 使用的模型名称
        """
        self.model_name = model_name
        self.fallback_model = "gemini-2.5-flash"  # 备用模型
        self.current_model = model_name  # 当前使用的模型
        self.model_switched = False  # 是否已切换到备用模型
        self.client = None
        self._initialize_client()
        
    def _initialize_client(self):
        """初始化Gemini客户端"""
        try:
            # 客户端从环境变量 GEMINI_API_KEY 获取API密钥
            self.client = genai.Client()
            logger.info("Gemini客户端初始化成功")
        except Exception as e:
            logger.error(f"初始化Gemini客户端失败: {e}")
            self.client = None

    def _should_switch_model(self, error_message: str) -> bool:
        """
        判断是否应该切换到备用模型

        Args:
            error_message: 错误信息

        Returns:
            bool: 是否应该切换模型
        """
        # 检查是否是模型过载错误
        overload_indicators = [
            "503 UNAVAILABLE",
            "The model is overloaded",
            "overloaded",
            "UNAVAILABLE",
            "503"
        ]

        error_lower = error_message.lower()
        return any(indicator.lower() in error_lower for indicator in overload_indicators)

    def _switch_to_fallback_model(self):
        """切换到备用模型"""
        if not self.model_switched:
            self.current_model = self.fallback_model
            self.model_switched = True
            logger.warning(f"检测到模型过载，切换到备用模型: {self.fallback_model}")
        else:
            logger.info(f"已在使用备用模型: {self.current_model}")

    def _is_analysis_error(self, result: Dict) -> bool:
        """
        判断分析结果是否为错误结果

        Args:
            result: 分析结果

        Returns:
            bool: 是否为错误结果
        """
        if not result:
            return True

        # 检查是否有error标记
        if result.get('error', False):
            return True

        # 检查reason字段是否包含错误信息
        reason = result.get('reason', '')
        if any(indicator in reason for indicator in ['分析过程出错', 'API响应解析错误', '503 UNAVAILABLE']):
            return True

        # 检查summary字段是否为错误信息
        summary = result.get('summary', '')
        if summary in ['分析失败', '分析失败，无法解析响应', '批量分析失败']:
            return True

        return False
    
    def _create_analysis_prompt(self, email_data: Dict) -> str:
        """
        创建邮件分析提示词
        
        Args:
            email_data: 邮件数据
            
        Returns:
            str: 分析提示词
        """
        prompt = f"""
请分析以下邮件内容，判断其重要性并提供分析结果。

邮件信息：
- 主题：{email_data.get('subject', '无主题')}
- 发件人：{email_data.get('from', '未知发件人')}
- 收件时间：{email_data.get('date', '未知时间')}

邮件正文：
{email_data.get('content', '无内容')[:2000]}  # 限制内容长度

请按照以下JSON格式返回分析结果：
{{
    "importance_level": "高/中/低",
    "importance_score": 1-10的数字评分,
    "category": "工作/个人/营销/通知/垃圾邮件/其他",
    "keywords": ["关键词1", "关键词2", "关键词3"],
    "summary": "邮件内容简要总结",
    "reason": "判断为重要/不重要的具体原因",
    "action_required": "是否需要采取行动(是/否)",
    "urgency": "紧急程度(紧急/一般/不紧急)"
}}

分析要点：
1. 工作相关邮件（会议通知、项目更新、任务分配等）通常重要性较高
2. 来自重要联系人的邮件需要特别关注
3. 包含截止日期、紧急标识的邮件优先级高
4. 营销邮件、垃圾邮件重要性较低
5. 个人重要事务（银行通知、账单、预约等）需要关注

请确保返回的是有效的JSON格式。
"""
        return prompt
    
    def analyze_email(self, email_data: Dict) -> Optional[Dict]:
        """
        分析单封邮件
        
        Args:
            email_data: 邮件数据
            
        Returns:
            Dict: 分析结果
        """
        if not self.client:
            logger.error("Gemini客户端未初始化")
            return None
            
        try:
            # 创建分析提示词
            prompt = self._create_analysis_prompt(email_data)
            
            # 调用Gemini API
            response = self.client.models.generate_content(
                model=self.current_model,
                contents=prompt
            )
            
            # 解析响应
            response_text = response.text.strip()
            logger.info(f"收到Gemini响应: {response_text[:200]}...")
            
            # 尝试解析JSON
            try:
                # 提取JSON部分（可能包含在代码块中）
                if "```json" in response_text:
                    json_start = response_text.find("```json") + 7
                    json_end = response_text.find("```", json_start)
                    json_text = response_text[json_start:json_end].strip()
                elif "```" in response_text:
                    json_start = response_text.find("```") + 3
                    json_end = response_text.find("```", json_start)
                    json_text = response_text[json_start:json_end].strip()
                else:
                    json_text = response_text
                
                analysis_result = json.loads(json_text)
                
                # 添加原始邮件信息
                analysis_result['email_id'] = email_data.get('id')
                analysis_result['email_subject'] = email_data.get('subject')
                analysis_result['email_from'] = email_data.get('from')
                analysis_result['email_date'] = email_data.get('date')
                analysis_result['analysis_time'] = datetime.now().isoformat()
                analysis_result['analyzed_by_model'] = self.current_model  # 记录使用的模型

                logger.info(f"邮件分析完成: {email_data.get('subject', '无主题')[:50]} (模型: {self.current_model})")
                return analysis_result
                
            except json.JSONDecodeError as e:
                logger.error(f"解析JSON响应失败: {e}")
                logger.error(f"原始响应: {response_text}")
                
                # 返回基础分析结果
                return {
                    'email_id': email_data.get('id'),
                    'email_subject': email_data.get('subject'),
                    'email_from': email_data.get('from'),
                    'email_date': email_data.get('date'),
                    'importance_level': '未知',
                    'importance_score': 0,  # 错误时设为0分，避免误判为重要邮件
                    'category': '分析失败',
                    'summary': '分析失败，无法解析响应',
                    'reason': f'API响应解析错误: {str(e)}',
                    'analysis_time': datetime.now().isoformat(),
                    'analyzed_by_model': self.current_model,
                    'error': True
                }
                
        except Exception as e:
            error_msg = str(e)
            logger.error(f"分析邮件时出错: {error_msg}")

            # 检查是否需要切换模型
            if self._should_switch_model(error_msg):
                self._switch_to_fallback_model()

            return {
                'email_id': email_data.get('id'),
                'email_subject': email_data.get('subject'),
                'email_from': email_data.get('from'),
                'email_date': email_data.get('date'),
                'importance_level': '未知',
                'importance_score': 0,  # 错误时设为0分，避免误判为重要邮件
                'category': '分析失败',
                'summary': '分析失败',
                'reason': f'分析过程出错: {error_msg}',
                'analysis_time': datetime.now().isoformat(),
                'analyzed_by_model': self.current_model,
                'error': True
            }
    
    def _create_batch_analysis_prompt(self, emails_batch: List[Dict]) -> str:
        """
        创建批量邮件分析提示词

        Args:
            emails_batch: 邮件批次数据

        Returns:
            str: 批量分析提示词
        """
        prompt = f"""
请分析以下 {len(emails_batch)} 封邮件的内容，判断每封邮件的重要性并提供分析结果。

邮件列表：
"""

        for i, email_data in enumerate(emails_batch, 1):
            prompt += f"""
邮件 {i}：
- 主题：{email_data.get('subject', '无主题')}
- 发件人：{email_data.get('from', '未知发件人')}
- 收件时间：{email_data.get('date', '未知时间')}
- 邮件正文：{email_data.get('content', '无内容')[:1500]}  # 限制内容长度

"""

        prompt += f"""
请按照以下JSON格式返回分析结果，返回一个包含 {len(emails_batch)} 个邮件分析结果的数组：

{{
    "results": [
        {{
            "email_index": 1,
            "importance_level": "高/中/低",
            "importance_score": 1-10的数字评分,
            "category": "工作/个人/营销/通知/垃圾邮件/其他",
            "keywords": ["关键词1", "关键词2", "关键词3"],
            "summary": "邮件内容简要总结",
            "reason": "判断为重要/不重要的具体原因",
            "action_required": "是否需要采取行动(是/否)",
            "urgency": "紧急程度(紧急/一般/不紧急)"
        }},
        {{
            "email_index": 2,
            ...
        }}
        // ... 其他邮件的分析结果
    ]
}}

分析要点：
1. 工作相关邮件（会议通知、项目更新、任务分配等）通常重要性较高
2. 来自重要联系人的邮件需要特别关注
3. 包含截止日期、紧急标识的邮件优先级高
4. 营销邮件、垃圾邮件重要性较低
5. 个人重要事务（银行通知、账单、预约等）需要关注

请确保返回的是有效的JSON格式，并且results数组包含所有 {len(emails_batch)} 封邮件的分析结果。
"""
        return prompt

    def analyze_emails_batch_optimized(self, emails: List[Dict], batch_size: int = 10, delay: float = 1.0) -> List[Dict]:
        """
        优化的批量分析邮件（一次提交多封邮件）

        Args:
            emails: 邮件列表
            batch_size: 每批处理的邮件数量
            delay: 批次间隔时间（秒）

        Returns:
            List[Dict]: 分析结果列表
        """
        results = []
        total_emails = len(emails)

        logger.info(f"开始优化批量分析 {total_emails} 封邮件，批次大小: {batch_size}")

        # 分批处理邮件
        for batch_start in range(0, total_emails, batch_size):
            batch_end = min(batch_start + batch_size, total_emails)
            emails_batch = emails[batch_start:batch_end]
            batch_num = (batch_start // batch_size) + 1
            total_batches = (total_emails + batch_size - 1) // batch_size

            logger.info(f"正在处理第 {batch_num}/{total_batches} 批邮件 ({len(emails_batch)} 封)")

            batch_results = self._analyze_emails_batch_single_request(emails_batch, batch_start)
            if batch_results:
                results.extend(batch_results)

            # 添加延迟避免API限制（除了最后一批）
            if batch_end < total_emails:
                logger.info(f"等待 {delay} 秒后处理下一批...")
                time.sleep(delay)

        logger.info(f"优化批量分析完成，成功分析 {len(results)} 封邮件")
        return results

    def _analyze_emails_batch_single_request(self, emails_batch: List[Dict], batch_start_index: int) -> List[Dict]:
        """
        单次请求分析一批邮件

        Args:
            emails_batch: 邮件批次
            batch_start_index: 批次起始索引

        Returns:
            List[Dict]: 分析结果列表
        """
        if not self.client:
            logger.error("Gemini客户端未初始化")
            return []

        try:
            # 创建批量分析提示词
            prompt = self._create_batch_analysis_prompt(emails_batch)

            # 调用Gemini API
            logger.info(f"向Gemini提交 {len(emails_batch)} 封邮件进行批量分析... (模型: {self.current_model})")
            response = self.client.models.generate_content(
                model=self.current_model,
                contents=prompt
            )

            # 解析响应
            response_text = response.text.strip()
            logger.info(f"收到Gemini批量分析响应: {response_text[:200]}...")

            # 尝试解析JSON
            try:
                # 提取JSON部分（可能包含在代码块中）
                if "```json" in response_text:
                    json_start = response_text.find("```json") + 7
                    json_end = response_text.find("```", json_start)
                    json_text = response_text[json_start:json_end].strip()
                elif "```" in response_text:
                    json_start = response_text.find("```") + 3
                    json_end = response_text.find("```", json_start)
                    json_text = response_text[json_start:json_end].strip()
                else:
                    json_text = response_text

                batch_response = json.loads(json_text)

                # 处理批量分析结果
                results = []
                batch_results = batch_response.get('results', [])

                for i, email_data in enumerate(emails_batch):
                    # 查找对应的分析结果
                    analysis_result = None
                    for result in batch_results:
                        if result.get('email_index') == i + 1:
                            analysis_result = result
                            break

                    if analysis_result:
                        # 添加原始邮件信息
                        analysis_result['email_id'] = email_data.get('id')
                        analysis_result['email_subject'] = email_data.get('subject')
                        analysis_result['email_from'] = email_data.get('from')
                        analysis_result['email_date'] = email_data.get('date')
                        analysis_result['analysis_time'] = datetime.now().isoformat()
                        analysis_result['analyzed_by_model'] = self.current_model  # 记录使用的模型
                        analysis_result['batch_index'] = batch_start_index + i

                        # 移除email_index字段（不再需要）
                        analysis_result.pop('email_index', None)

                        results.append(analysis_result)
                        logger.info(f"邮件分析完成: {email_data.get('subject', '无主题')[:50]} (模型: {self.current_model})")
                    else:
                        # 如果没有找到对应的分析结果，创建默认结果
                        logger.warning(f"未找到邮件 {i+1} 的分析结果，使用默认值")
                        results.append({
                            'email_id': email_data.get('id'),
                            'email_subject': email_data.get('subject'),
                            'email_from': email_data.get('from'),
                            'email_date': email_data.get('date'),
                            'importance_level': '未知',
                            'importance_score': 0,  # 错误时设为0分
                            'category': '分析失败',
                            'summary': '批量分析中未返回结果',
                            'reason': '批量分析响应中缺少此邮件的结果',
                            'analysis_time': datetime.now().isoformat(),
                            'analyzed_by_model': self.current_model,
                            'batch_index': batch_start_index + i,
                            'error': True
                        })

                return results

            except json.JSONDecodeError as e:
                logger.error(f"解析批量分析JSON响应失败: {e}")
                logger.error(f"原始响应: {response_text}")

                # 返回基础分析结果
                return self._create_fallback_results(emails_batch, batch_start_index, f"JSON解析错误: {str(e)}")

        except Exception as e:
            error_msg = str(e)
            logger.error(f"批量分析邮件时出错: {error_msg}")

            # 检查是否需要切换模型
            if self._should_switch_model(error_msg):
                self._switch_to_fallback_model()

            return self._create_fallback_results(emails_batch, batch_start_index, f"分析过程出错: {error_msg}")

    def _create_fallback_results(self, emails_batch: List[Dict], batch_start_index: int, error_msg: str) -> List[Dict]:
        """
        创建失败时的备用分析结果

        Args:
            emails_batch: 邮件批次
            batch_start_index: 批次起始索引
            error_msg: 错误信息

        Returns:
            List[Dict]: 备用分析结果列表
        """
        results = []
        for i, email_data in enumerate(emails_batch):
            results.append({
                'email_id': email_data.get('id'),
                'email_subject': email_data.get('subject'),
                'email_from': email_data.get('from'),
                'email_date': email_data.get('date'),
                'importance_level': '未知',
                'importance_score': 0,  # 错误时设为0分
                'category': '分析失败',
                'summary': '批量分析失败',
                'reason': error_msg,
                'analysis_time': datetime.now().isoformat(),
                'analyzed_by_model': self.current_model,
                'batch_index': batch_start_index + i,
                'error': True
            })
        return results

    def analyze_emails_batch(self, emails: List[Dict], delay: float = 1.0, batch_size: int = 10, use_optimized: bool = True) -> List[Dict]:
        """
        批量分析邮件（支持优化和传统模式）

        Args:
            emails: 邮件列表
            delay: 请求间隔时间（秒）
            batch_size: 批处理大小（仅在优化模式下使用）
            use_optimized: 是否使用优化的批量分析模式

        Returns:
            List[Dict]: 分析结果列表
        """
        if use_optimized and batch_size > 1:
            return self.analyze_emails_batch_optimized(emails, batch_size, delay)
        else:
            # 传统的逐个分析模式
            results = []

            logger.info(f"开始传统批量分析 {len(emails)} 封邮件")

            for i, email_data in enumerate(emails, 1):
                logger.info(f"正在分析第 {i}/{len(emails)} 封邮件: {email_data.get('subject', '无主题')[:50]}")

                result = self.analyze_email(email_data)
                if result:
                    results.append(result)

                # 添加延迟避免API限制
                if i < len(emails):
                    time.sleep(delay)

            logger.info(f"传统批量分析完成，成功分析 {len(results)} 封邮件")
            return results
    
    def filter_important_emails(self, analysis_results: List[Dict], 
                              min_score: int = 7) -> List[Dict]:
        """
        筛选重要邮件
        
        Args:
            analysis_results: 分析结果列表
            min_score: 最低重要性评分
            
        Returns:
            List[Dict]: 重要邮件列表
        """
        important_emails = []
        
        for result in analysis_results:
            # 跳过分析失败的邮件
            if result.get('error') or self._is_analysis_error(result):
                continue

            importance_score = result.get('importance_score', 0)
            importance_level = result.get('importance_level', '').lower()

            # 确保评分有效（大于0）
            if importance_score <= 0:
                continue

            # 根据评分或重要性级别筛选
            if (importance_score >= min_score or
                importance_level in ['高', 'high'] or
                result.get('urgency', '').lower() in ['紧急', 'urgent']):
                important_emails.append(result)
        
        # 按重要性评分排序
        important_emails.sort(key=lambda x: x.get('importance_score', 0), reverse=True)
        
        logger.info(f"筛选出 {len(important_emails)} 封重要邮件")
        return important_emails
    
    def generate_summary_report(self, analysis_results: List[Dict]) -> str:
        """
        生成分析摘要报告
        
        Args:
            analysis_results: 分析结果列表
            
        Returns:
            str: 摘要报告
        """
        if not analysis_results:
            return "没有邮件分析结果"
        
        # 统计信息
        total_emails = len(analysis_results)
        high_importance = len([r for r in analysis_results 
                             if r.get('importance_level', '').lower() in ['高', 'high']])
        medium_importance = len([r for r in analysis_results 
                               if r.get('importance_level', '').lower() in ['中', 'medium']])
        low_importance = len([r for r in analysis_results 
                            if r.get('importance_level', '').lower() in ['低', 'low']])
        
        # 分类统计
        categories = {}
        models_used = {}
        error_count = 0

        for result in analysis_results:
            category = result.get('category', '其他')
            categories[category] = categories.get(category, 0) + 1

            # 统计使用的模型
            model = result.get('analyzed_by_model', '未知')
            models_used[model] = models_used.get(model, 0) + 1

            # 统计错误数量
            if result.get('error') or self._is_analysis_error(result):
                error_count += 1
        
        # 生成报告
        report = f"""
邮件分析摘要报告
================

总体统计：
- 分析邮件总数：{total_emails}
- 成功分析数量：{total_emails - error_count}
- 分析失败数量：{error_count}
- 高重要性邮件：{high_importance} 封
- 中重要性邮件：{medium_importance} 封
- 低重要性邮件：{low_importance} 封

模型使用统计：
"""

        for model, count in sorted(models_used.items(), key=lambda x: x[1], reverse=True):
            report += f"- {model}：{count} 封\n"

        report += f"\n邮件分类统计：\n"
        
        for category, count in sorted(categories.items(), key=lambda x: x[1], reverse=True):
            report += f"- {category}：{count} 封\n"
        
        # 重要邮件详情
        important_emails = self.filter_important_emails(analysis_results)
        if important_emails:
            report += f"\n重要邮件详情（共 {len(important_emails)} 封）：\n"
            report += "=" * 40 + "\n"
            
            for i, email in enumerate(important_emails[:10], 1):  # 只显示前10封
                report += f"\n{i}. 【{email.get('importance_level', '未知')}】{email.get('email_subject', '无主题')}\n"
                report += f"   发件人：{email.get('email_from', '未知')}\n"
                report += f"   评分：{email.get('importance_score', 0)}/10\n"
                report += f"   分类：{email.get('category', '其他')}\n"
                report += f"   分析模型：{email.get('analyzed_by_model', '未知')}\n"
                report += f"   原因：{email.get('reason', '无原因')}\n"
                if email.get('action_required') == '是':
                    report += f"   ⚠️ 需要采取行动\n"
                if email.get('urgency', '').lower() in ['紧急', 'urgent']:
                    report += f"   🚨 紧急处理\n"
        
        return report
