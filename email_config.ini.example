# 邮件分析系统配置文件示例
# 复制此文件为 email_config.ini 并填入您的配置信息

[EMAIL]
# 邮箱服务提供商 (qq, 163, gmail, outlook, sina)
provider = qq

# IMAP服务器地址
server = imap.qq.com

# IMAP端口 (通常为993)
port = 993

# 邮箱用户名 (完整邮箱地址)
username = <EMAIL>

# 邮箱密码或授权码 (QQ邮箱需要使用授权码)
password = your_password_or_auth_code

# 邮件文件夹 (通常为INBOX)
folder = INBOX

[ANALYSIS]
# Gemini模型名称
model_name = gemini-2.5-pro

# 批处理大小 (一次分析的邮件数量)
batch_size = 10

# 请求间隔时间 (秒，避免API限制)
delay_between_requests = 1.0

# 最低重要性评分 (1-10，高于此分数的邮件被认为重要)
min_importance_score = 7

[LOGGING]
# 日志级别 (DEBUG, INFO, WARNING, ERROR)
level = INFO

# 日志文件路径
log_file = email_analysis.log

# 日志文件最大大小 (字节)
max_log_size = 10485760

# 日志文件备份数量
backup_count = 5

[OUTPUT]
# 是否保存分析结果 (true/false)
save_results = true

# 结果文件路径
results_file = analysis_results.json

# 是否生成分析报告 (true/false)
generate_report = true

# 报告文件路径
report_file = analysis_report.txt
