"""
邮件分析系统演示脚本
展示系统的核心功能和使用方法
"""

import os
import sys
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config_manager import ConfigManager
from email_fetcher import get_email_server_config


def demo_config_management():
    """演示配置管理功能"""
    print("🔧 配置管理演示")
    print("-" * 40)
    
    # 创建配置管理器
    config_manager = ConfigManager("demo_config.ini")
    
    # 显示配置状态
    config_manager.print_config_status()
    
    # 演示配置更新
    print("\n📝 更新邮箱配置...")
    config_manager.update_email_config(
        provider="qq",
        server="imap.qq.com",
        port=993,
        username="<EMAIL>"
    )
    
    print("✅ 配置更新完成")


def demo_email_server_configs():
    """演示邮箱服务器配置"""
    print("\n📧 支持的邮箱服务器")
    print("-" * 40)
    
    providers = ['qq', '163', 'gmail', 'outlook', 'sina']
    
    for provider in providers:
        config = get_email_server_config(provider)
        if config:
            print(f"{provider.upper():>8}: {config['server']}:{config['port']}")
        else:
            print(f"{provider.upper():>8}: 不支持")


def demo_analysis_prompt():
    """演示分析提示词"""
    print("\n🤖 AI分析示例")
    print("-" * 40)
    
    # 模拟邮件数据
    sample_email = {
        'subject': '【紧急】项目进度会议通知',
        'from': '<EMAIL>',
        'date': '2024-01-15 14:30:00',
        'content': '''
        各位同事：
        
        由于项目进度紧张，需要召开紧急会议讨论当前进展和后续安排。
        
        会议时间：明天（1月16日）上午10:00
        会议地点：会议室A
        参会人员：项目组全体成员
        
        请务必准时参加，谢谢！
        
        项目经理
        '''
    }
    
    print("📨 示例邮件:")
    print(f"主题: {sample_email['subject']}")
    print(f"发件人: {sample_email['from']}")
    print(f"时间: {sample_email['date']}")
    print(f"内容: {sample_email['content'][:100]}...")
    
    print("\n🔍 预期分析结果:")
    print("- 重要性级别: 高")
    print("- 重要性评分: 9/10")
    print("- 邮件分类: 工作")
    print("- 关键词: ['紧急', '会议', '项目', '进度']")
    print("- 需要行动: 是")
    print("- 紧急程度: 紧急")


def demo_usage_examples():
    """演示使用示例"""
    print("\n💡 使用示例")
    print("-" * 40)
    
    examples = [
        ("基本使用", "python main.py"),
        ("分析20封邮件", "python main.py --limit 20"),
        ("分析指定文件夹", "python main.py --folder '重要邮件'"),
        ("设置最低评分", "python main.py --min-score 8"),
        ("检查配置", "python main.py --check-config"),
        ("使用自定义配置", "python main.py --config my_config.ini"),
    ]
    
    for desc, cmd in examples:
        print(f"{desc:>12}: {cmd}")


def demo_security_tips():
    """演示安全建议"""
    print("\n🔒 安全建议")
    print("-" * 40)
    
    tips = [
        "使用邮箱授权码而不是登录密码",
        "通过环境变量设置敏感信息",
        "定期更换API密钥和授权码",
        "不要在代码中硬编码密码",
        "保护配置文件的访问权限",
        "定期清理日志文件中的敏感信息"
    ]
    
    for i, tip in enumerate(tips, 1):
        print(f"{i}. {tip}")


def demo_file_structure():
    """演示文件结构"""
    print("\n📁 系统文件结构")
    print("-" * 40)
    
    structure = """
邮件分析/
├── main.py                    # 主程序入口
├── email_fetcher.py          # 邮件获取模块
├── email_analyzer.py         # AI分析模块
├── config_manager.py         # 配置管理模块
├── setup.py                  # 安装脚本
├── requirements.txt          # 依赖列表
├── email_config.ini.example  # 配置模板
├── README.md                 # 使用说明
└── 输出文件/
    ├── analysis_results_*.json  # 分析结果
    ├── analysis_report_*.txt    # 分析报告
    └── email_analysis.log       # 系统日志
"""
    
    print(structure)


def main():
    """主演示函数"""
    print("🚀 邮件分析系统功能演示")
    print("=" * 50)
    print(f"演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 配置管理演示
        demo_config_management()
        
        # 邮箱服务器配置演示
        demo_email_server_configs()
        
        # AI分析演示
        demo_analysis_prompt()
        
        # 使用示例演示
        demo_usage_examples()
        
        # 安全建议演示
        demo_security_tips()
        
        # 文件结构演示
        demo_file_structure()
        
        print("\n" + "=" * 50)
        print("✅ 演示完成！")
        print("\n📖 详细使用说明请查看 README.md")
        print("🔧 运行 python setup.py 开始安装配置")
        print("🚀 运行 python main.py 开始使用系统")
        
    except Exception as e:
        print(f"\n❌ 演示过程出错: {e}")
    
    finally:
        # 清理演示配置文件
        demo_config = "demo_config.ini"
        if os.path.exists(demo_config):
            os.remove(demo_config)
            print(f"\n🧹 已清理演示文件: {demo_config}")


if __name__ == "__main__":
    main()
